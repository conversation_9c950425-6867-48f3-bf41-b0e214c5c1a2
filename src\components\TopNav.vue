<template>
    <div class="top-nav">
        <div class="nav-container">
            <div class="nav-left">
                <button class="nav-button" :class="{ active: currentTab === 'public' }"
                    @click="handleTabClick('public')">
                    公共场所在线监测
                </button>
                <button class="nav-button" :class="{ active: currentTab === 'radiation' }"
                    @click="handleTabClick('radiation')">
                    放射卫生在线监测
                </button>
            </div>
            <h1 class="nav-title">
                漳平市卫生监督管理系统
            </h1>
            <div class="nav-right">
                <button class="nav-button" :class="{ active: currentTab === 'occupational' }"
                    @click="handleTabClick('occupational')">
                    职业病危害因素在线监测
                </button>
                <button class="nav-button" :class="{ active: currentTab === 'video' }" @click="handleTabClick('video')">
                    实时视频监控
                </button>
                <button class="nav-button" :class="{ active: currentTab === 'support' }"
                    @click="handleTabClick('support')">
                    支撑应用管理
                </button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { useRouter, } from 'vue-router';

const router = useRouter();
const currentTab = ref(''); // 初始选中项，可根据需求调整


// 导航项与路由名称的映射关系
const tabToRouteMap = {
    'public': 'PublicPlaceMonitor',
    'radiation': 'RadiationHealthMonitor',
    'occupational': 'OccupationalHazardMonitor',
    'video': 'RealTimeVideoMonitor',
    'support': 'SupportAppManagement'
};

const handleTabClick = (tab) => {
    // 更新当前选中的标签
    currentTab.value = tab;

    // 获取对应的路由名称
    const routeName = tabToRouteMap[tab];

    if (routeName) {
        // 执行路由跳转
        router.push({ name: routeName })
            .catch(error => {
                console.error('路由跳转失败:', error);
                // 可以在这里添加错误处理逻辑，如显示提示信息
            });
    } else {
        console.warn(`未找到标签 "${tab}" 对应的路由`);
    }
};


</script>

<style scoped lang="scss">
@use '@/assets/base.scss' as *;

.top-nav {
    background-color: $secondary-color;
    @include flex-center;
    height: 60px;
    background-size: cover;
    background-repeat: no-repeat;
}

.nav-container {
    @include flex-center;
    position: relative;
    width: 100%;
    max-width: 1920px;
    padding: 0 $spacing-md;
}

.nav-left,
.nav-right {
    display: flex;
    align-items: center;
    gap: $spacing-xl;
}

.nav-left {
    margin-left: 14vw;
    margin-right: auto;
}

.nav-right {
    margin-left: auto;
    margin-right: 3vw;
}

.nav-button {
    padding: $spacing-sm $spacing-md;
    color: $text-white;
    background: transparent;
    border: none;
    border-radius: $border-radius-md;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 18px;
    text-align: center;
    font-style: normal;
    text-transform: none;

    &.active {
        background-color: $primary-color;
    }

    &:hover:not(.active) {
        background-color: rgba(255, 255, 255, 0.1);
    }
}

.nav-title {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    color: $text-white;
    margin: 0;
    width: 331px;
    font-family: AlibabaPuHuiTi_2_55_Regular;
    font-size: 30px;
    line-height: 50px;
    text-align: center;
    font-style: normal;
    text-transform: none;
}
</style>